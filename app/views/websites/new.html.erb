<div class="mx-auto max-w-2xl px-4 py-8 sm:px-6 lg:px-8">
  <div class="md:flex md:items-center md:justify-between">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
        Add New Website
      </h2>
    </div>
  </div>

  <div class="mt-8">
    <%= form_with model: @website, local: true, class: "space-y-6" do |form| %>
      <% if @website.errors.any? %>
        <div class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                There were <%= pluralize(@website.errors.count, "error") %> with your submission
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul role="list" class="list-disc space-y-1 pl-5">
                  <% @website.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div>
        <%= form.label :name, class: "block text-sm font-medium leading-6 text-gray-900" %>
        <div class="mt-2">
          <%= form.text_field :name, 
              class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
              placeholder: "My Website" %>
        </div>
        <p class="mt-2 text-sm text-gray-500">A friendly name to identify this website.</p>
      </div>

      <div>
        <%= form.label :url, "Website URL", class: "block text-sm font-medium leading-6 text-gray-900" %>
        <div class="mt-2">
          <%= form.text_field :url, 
              class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
              placeholder: "https://example.com" %>
        </div>
        <p class="mt-2 text-sm text-gray-500">The full URL of the website you want to monitor.</p>
      </div>

      <div>
        <%= form.label :monitor_frequency, class: "block text-sm font-medium leading-6 text-gray-900" %>
        <div class="mt-2">
          <%= form.select :monitor_frequency, 
              options_for_select([
                ['Every hour', 'hourly'],
                ['Every 6 hours', 'six_hours'],
                ['Daily', 'daily'],
                ['Weekly', 'weekly'],
                ['Monthly', 'monthly']
              ], @website.monitor_frequency || 'daily'),
              {},
              class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
        </div>
        <p class="mt-2 text-sm text-gray-500">How often should we check your website's performance?</p>
      </div>

      <fieldset>
        <legend class="text-sm font-semibold leading-6 text-gray-900">Monitoring Settings</legend>
        <div class="mt-4 space-y-4">
          <div class="relative flex items-start">
            <div class="flex h-6 items-center">
              <%= check_box_tag "website[monitoring_settings][enable_performance_monitoring]", 
                  "1", 
                  @website.monitoring_settings&.dig('enable_performance_monitoring') != false,
                  class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
            </div>
            <div class="ml-3 text-sm leading-6">
              <label for="website_monitoring_settings_enable_performance_monitoring" class="font-medium text-gray-900">
                Performance Monitoring
              </label>
              <p class="text-gray-500">Track Core Web Vitals and page load metrics</p>
            </div>
          </div>

          <div class="relative flex items-start">
            <div class="flex h-6 items-center">
              <%= check_box_tag "website[monitoring_settings][enable_seo_monitoring]", 
                  "1", 
                  @website.monitoring_settings&.dig('enable_seo_monitoring') != false,
                  class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
            </div>
            <div class="ml-3 text-sm leading-6">
              <label for="website_monitoring_settings_enable_seo_monitoring" class="font-medium text-gray-900">
                SEO Monitoring
              </label>
              <p class="text-gray-500">Monitor SEO metrics and meta tags</p>
            </div>
          </div>

          <div class="relative flex items-start">
            <div class="flex h-6 items-center">
              <%= check_box_tag "website[monitoring_settings][enable_security_monitoring]", 
                  "1", 
                  @website.monitoring_settings&.dig('enable_security_monitoring') != false,
                  class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
            </div>
            <div class="ml-3 text-sm leading-6">
              <label for="website_monitoring_settings_enable_security_monitoring" class="font-medium text-gray-900">
                Security Monitoring
              </label>
              <p class="text-gray-500">Check for security headers and HTTPS configuration</p>
            </div>
          </div>

          <div class="relative flex items-start">
            <div class="flex h-6 items-center">
              <%= check_box_tag "website[monitoring_settings][enable_accessibility_monitoring]", 
                  "1", 
                  @website.monitoring_settings&.dig('enable_accessibility_monitoring') != false,
                  class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
            </div>
            <div class="ml-3 text-sm leading-6">
              <label for="website_monitoring_settings_enable_accessibility_monitoring" class="font-medium text-gray-900">
                Accessibility Monitoring
              </label>
              <p class="text-gray-500">Check for accessibility issues and WCAG compliance</p>
            </div>
          </div>
        </div>
      </fieldset>

      <div class="relative flex items-start">
        <div class="flex h-6 items-center">
          <%= form.check_box :active, 
              checked: @website.active != false,
              class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
        </div>
        <div class="ml-3 text-sm leading-6">
          <label for="website_active" class="font-medium text-gray-900">
            Active Monitoring
          </label>
          <p class="text-gray-500">Enable automatic monitoring for this website</p>
        </div>
      </div>

      <div class="mt-6 flex items-center justify-end gap-x-6">
        <%= link_to "Cancel", websites_path, 
            class: "text-sm font-semibold leading-6 text-gray-900" %>
        <%= form.submit "Add Website", 
            class: "rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
      </div>
    <% end %>
  </div>
</div>