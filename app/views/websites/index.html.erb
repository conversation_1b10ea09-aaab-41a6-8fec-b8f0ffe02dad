<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-2xl font-semibold leading-6 text-gray-900">Websites</h1>
      <p class="mt-2 text-sm text-gray-700">
        Monitor and analyze the performance of your websites.
      </p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to "Add website", new_website_path, 
          class: "block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Total Websites</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @statistics[:total_websites] %>
      </dd>
    </div>
    
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Active Websites</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @statistics[:active_websites] %>
      </dd>
    </div>
    
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Total Audits</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @statistics[:total_audits] %>
      </dd>
    </div>
    
    <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
      <dt class="truncate text-sm font-medium text-gray-500">Audits This Month</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
        <%= @statistics[:audits_this_month] %>
      </dd>
    </div>
  </div>

  <!-- Websites Table -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">URL</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Current Score</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Last Monitored</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <% if @websites.any? %>
                <% @websites.each do |website| %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                      <%= link_to website.name, website_path(website), class: "text-indigo-600 hover:text-indigo-900" %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <%= website.url %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <% if website.active? %>
                        <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                          Active
                        </span>
                      <% else %>
                        <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                          Inactive
                        </span>
                      <% end %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <% if website.current_score %>
                        <span class="<%= website.current_score >= 90 ? 'text-green-600' : website.current_score >= 50 ? 'text-yellow-600' : 'text-red-600' %> font-medium">
                          <%= website.current_score %>
                        </span>
                      <% else %>
                        <span class="text-gray-400">—</span>
                      <% end %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      <%= website.last_monitored_at ? time_ago_in_words(website.last_monitored_at) + " ago" : "Never" %>
                    </td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <%= link_to "View", website_path(website), class: "text-indigo-600 hover:text-indigo-900 mr-4" %>
                      <%= link_to "Edit", edit_website_path(website), class: "text-indigo-600 hover:text-indigo-900 mr-4" %>
                      <%= button_to "Monitor", monitor_website_path(website), method: :post, 
                          class: "inline text-indigo-600 hover:text-indigo-900",
                          data: { turbo: true } %>
                    </td>
                  </tr>
                <% end %>
              <% else %>
                <tr>
                  <td colspan="6" class="px-3 py-8 text-center text-sm text-gray-500">
                    <div class="mx-auto max-w-sm">
                      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      <h3 class="mt-2 text-sm font-semibold text-gray-900">No websites</h3>
                      <p class="mt-1 text-sm text-gray-500">Get started by adding your first website to monitor.</p>
                      <div class="mt-6">
                        <%= link_to "Add website", new_website_path, 
                            class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
                      </div>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <% if @websites.any? %>
    <!-- Pagination -->
    <div class="mt-4">
      <%= paginate @websites %>
    </div>
  <% end %>
</div>