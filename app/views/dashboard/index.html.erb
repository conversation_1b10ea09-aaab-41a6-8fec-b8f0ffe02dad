<% content_for :page_header do %>
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-gray-900">Dashboard</h1>
      <nav class="mt-1 flex text-sm text-gray-600">
        <span>QuickFixPro</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">Dashboard</span>
      </nav>
    </div>
    <div class="flex items-center space-x-3">
      <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
        </svg>
        Add Website
      </button>
      <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
        Generate Report
      </button>
    </div>
  </div>
<% end %>

<!-- Welcome Section -->
<div class="mb-6">
  <div class="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-6 text-white">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-semibold mb-2">Welcome back, <%= current_user.first_name %>! 👋</h2>
        <p class="text-blue-100">Here's what's happening with your websites today.</p>
      </div>
      <div class="hidden lg:block">
        <svg class="w-32 h-32 text-blue-400 opacity-50" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
        </svg>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards Row -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
  <!-- Total Websites Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
        </svg>
      </div>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        +12%
      </span>
    </div>
    <h3 class="text-2xl font-bold text-gray-900"><%= @total_websites %></h3>
    <p class="text-sm text-gray-500 mt-1">Total Websites</p>
  </div>
  
  <!-- Active Websites Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </div>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        Active
      </span>
    </div>
    <h3 class="text-2xl font-bold text-gray-900"><%= @active_websites %></h3>
    <p class="text-sm text-gray-500 mt-1">Active Websites</p>
  </div>
  
  <!-- Average Score Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
        </svg>
      </div>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        +5.2%
      </span>
    </div>
    <h3 class="text-2xl font-bold text-gray-900"><%= @average_score %>%</h3>
    <p class="text-sm text-gray-500 mt-1">Average Score</p>
  </div>
  
  <!-- Recent Audits Card -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
        </svg>
      </div>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
        Today
      </span>
    </div>
    <h3 class="text-2xl font-bold text-gray-900"><%= @recent_audits.count %></h3>
    <p class="text-sm text-gray-500 mt-1">Recent Audits</p>
  </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Performance Trends Chart (2 columns wide) -->
  <div class="lg:col-span-2">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Performance Trends</h3>
        <div class="flex items-center space-x-2">
          <button class="px-3 py-1 text-sm text-gray-600 hover:text-gray-900">Day</button>
          <button class="px-3 py-1 text-sm text-white bg-blue-600 rounded">Week</button>
          <button class="px-3 py-1 text-sm text-gray-600 hover:text-gray-900">Month</button>
        </div>
      </div>
      
      <!-- Simple Chart Placeholder -->
      <div class="h-64 flex items-end space-x-2">
        <% if @performance_trends.any? %>
          <% max_score = @performance_trends.map { |t| t[:score] }.max.to_f %>
          <% @performance_trends.last(14).each do |trend| %>
            <% height = max_score > 0 ? (trend[:score] / max_score * 100) : 0 %>
            <div class="flex-1 bg-blue-500 hover:bg-blue-600 rounded-t transition-colors relative group" 
                 style="height: <%= height %>%">
              <div class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 hidden group-hover:block">
                <div class="bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                  <%= trend[:score] %>%
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1">
                    <div class="border-4 border-transparent border-t-gray-900"></div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="w-full h-full flex items-center justify-center text-gray-400">
            No performance data available yet
          </div>
        <% end %>
      </div>
      
      <div class="mt-4 flex items-center justify-between text-xs text-gray-500">
        <span>Last 14 days</span>
        <span>Average: <%= @average_score %>%</span>
      </div>
    </div>
  </div>
  
  <!-- Recent Activity -->
  <div class="lg:col-span-1">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
      
      <div class="space-y-4">
        <% if @recent_activity.any? %>
          <% @recent_activity.first(5).each do |activity| %>
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <% if activity[:type] == 'audit_completed' %>
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                <% elsif activity[:type] == 'website_added' %>
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                <% else %>
                  <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                <% end %>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900"><%= activity[:message] %></p>
                <p class="text-xs text-gray-500 mt-1">
                  <%= time_ago_in_words(activity[:timestamp]) %> ago
                </p>
              </div>
            </div>
          <% end %>
        <% else %>
          <p class="text-sm text-gray-500">No recent activity</p>
        <% end %>
      </div>
      
      <div class="mt-4 pt-4 border-t border-gray-200">
        <a href="#" class="text-sm text-blue-600 hover:text-blue-700 font-medium">View all activity →</a>
      </div>
    </div>
  </div>
</div>

<!-- Bottom Row -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
  <!-- Top Performing Websites -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Top Performing Websites</h3>
      <a href="<%= websites_path %>" class="text-sm text-blue-600 hover:text-blue-700">View all</a>
    </div>
    
    <div class="space-y-3">
      <% @websites.includes(:latest_audit_report).limit(5).each do |website| %>
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
              </svg>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900"><%= website.name %></p>
              <p class="text-xs text-gray-500"><%= website.display_url %></p>
            </div>
          </div>
          <div class="text-right">
            <% score = website.current_score || 0 %>
            <p class="text-lg font-semibold <%= score >= 80 ? 'text-green-600' : score >= 60 ? 'text-yellow-600' : 'text-red-600' %>">
              <%= score %>%
            </p>
            <p class="text-xs text-gray-500">Score</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  
  <!-- Critical Issues -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Critical Issues</h3>
      <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
        <%= @critical_recommendations.count %> issues
      </span>
    </div>
    
    <div class="space-y-3">
      <% if @critical_recommendations.any? %>
        <% @critical_recommendations.first(5).each do |recommendation| %>
          <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900"><%= recommendation.title %></p>
                <p class="text-xs text-gray-600 mt-1"><%= recommendation.website.name %></p>
              </div>
              <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">Critical</span>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-8">
          <svg class="w-12 h-12 text-green-500 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <p class="text-sm text-gray-600">No critical issues found!</p>
          <p class="text-xs text-gray-500 mt-1">Your websites are performing well</p>
        </div>
      <% end %>
    </div>
    
    <% if @critical_recommendations.any? %>
      <div class="mt-4 pt-4 border-t border-gray-200">
        <a href="#" class="text-sm text-blue-600 hover:text-blue-700 font-medium">View all issues →</a>
      </div>
    <% end %>
  </div>
</div>