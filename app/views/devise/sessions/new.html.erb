<% content_for :main_classes, "min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4" %>
<% content_for :no_header, true %>
<% content_for :no_footer, true %>

<div class="w-full max-w-6xl bg-white rounded-2xl shadow-2xl overflow-hidden">
  <div class="flex flex-col lg:flex-row">
    <!-- Left Column - Illustration & Branding -->
    <div class="lg:w-1/2 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 p-12 flex flex-col justify-center relative overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-32 h-32 bg-white rounded-full"></div>
        <div class="absolute bottom-20 right-10 w-24 h-24 bg-white rounded-full"></div>
        <div class="absolute top-1/3 right-1/4 w-16 h-16 bg-white rounded-full"></div>
      </div>
      
      <!-- Logo -->
      <div class="relative z-10 mb-8">
        <%= link_to unauthenticated_root_path, class: "text-4xl font-bold text-white flex items-center" do %>
          <div class="w-12 h-12 bg-white rounded-xl flex items-center justify-center mr-4">
            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M13 7H7v6h6V7z" clip-rule="evenodd"/>
              <path fill-rule="evenodd" d="M13 15H7a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2zM7 6a1 1 0 00-1 1v6a1 1 0 001 1h6a1 1 0 001-1V7a1 1 0 00-1-1H7z" clip-rule="evenodd"/>
            </svg>
          </div>
          QuickFixPro
        <% end %>
      </div>

      <!-- Illustration -->
      <div class="relative z-10 text-center mb-8">
        <div class="inline-block p-8 bg-white bg-opacity-20 rounded-3xl backdrop-blur-sm">
          <svg class="w-48 h-48 mx-auto text-white" fill="currentColor" viewBox="0 0 100 100">
            <!-- User Icon with Dashboard -->
            <circle cx="35" cy="25" r="8" opacity="0.8"/>
            <path d="M35 35c-8 0-15 4-15 10v5h30v-5c0-6-7-10-15-10z" opacity="0.8"/>
            
            <!-- Dashboard Elements -->
            <rect x="55" y="15" width="30" height="20" rx="3" opacity="0.6"/>
            <rect x="58" y="18" width="8" height="2" opacity="0.9"/>
            <rect x="58" y="21" width="12" height="2" opacity="0.7"/>
            <rect x="58" y="24" width="6" height="2" opacity="0.9"/>
            <rect x="58" y="27" width="10" height="2" opacity="0.7"/>
            
            <!-- Charts/Analytics -->
            <rect x="55" y="40" width="30" height="15" rx="3" opacity="0.6"/>
            <polyline points="60,50 65,45 70,48 75,42 80,46" stroke="currentColor" stroke-width="2" fill="none" opacity="0.9"/>
            
            <!-- Performance Metrics -->
            <rect x="55" y="60" width="30" height="15" rx="3" opacity="0.6"/>
            <circle cx="62" cy="67" r="3" opacity="0.9"/>
            <circle cx="70" cy="67" r="3" opacity="0.7"/>
            <circle cx="78" cy="67" r="3" opacity="0.9"/>
            
            <!-- Speed/Performance Lines -->
            <g opacity="0.4">
              <path d="M15 80 Q50 60 85 80" stroke="currentColor" stroke-width="2" fill="none"/>
              <path d="M15 85 Q50 65 85 85" stroke="currentColor" stroke-width="2" fill="none"/>
              <path d="M15 90 Q50 70 85 90" stroke="currentColor" stroke-width="2" fill="none"/>
            </g>
          </svg>
        </div>
      </div>

      <!-- Welcome Text -->
      <div class="relative z-10 text-center text-white">
        <h2 class="text-3xl font-bold mb-4">Welcome Back!</h2>
        <p class="text-xl opacity-90 leading-relaxed">
          Ready to optimize your website performance? 
          <br>Sign in to access your dashboard and analytics.
        </p>
      </div>

      <!-- Features List -->
      <div class="relative z-10 mt-8 space-y-3">
        <div class="flex items-center text-white text-sm opacity-90">
          <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
          Real-time performance monitoring
        </div>
        <div class="flex items-center text-white text-sm opacity-90">
          <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
          Detailed audit reports & insights
        </div>
        <div class="flex items-center text-white text-sm opacity-90">
          <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
          Advanced analytics & comparisons
        </div>
      </div>
    </div>

    <!-- Right Column - Login Form -->
    <div class="lg:w-1/2 p-12 flex flex-col justify-center">
      <div class="max-w-md mx-auto w-full">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Sign In</h1>
          <p class="text-gray-600">Enter your credentials to access your account</p>
        </div>

        <!-- Login Form -->
        <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: "space-y-6" }) do |f| %>
          <!-- Email Field -->
          <div>
            <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-2" do %>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                </svg>
                Email Address
              </div>
            <% end %>
            <%= f.email_field :email, 
                autofocus: true, 
                autocomplete: "email",
                class: "mt-1 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
                placeholder: "Enter your email address" %>
          </div>

          <!-- Password Field -->
          <div>
            <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-2" do %>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                </svg>
                Password
              </div>
            <% end %>
            <%= f.password_field :password, 
                autocomplete: "current-password",
                class: "mt-1 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",
                placeholder: "Enter your password" %>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="flex items-center justify-between">
            <% if devise_mapping.rememberable? %>
              <div class="flex items-center">
                <%= f.check_box :remember_me, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
                <%= f.label :remember_me, "Remember me", class: "ml-2 block text-sm text-gray-700" %>
              </div>
            <% end %>

            <div class="text-sm">
              <%= link_to "Forgot password?", new_password_path(resource_name), 
                  class: "font-medium text-blue-600 hover:text-blue-500 transition-colors" %>
            </div>
          </div>

          <!-- Submit Button -->
          <div>
            <%= f.submit "Sign In", 
                class: "w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105" %>
          </div>

          <!-- Error Messages -->
          <% if resource.errors.any? %>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                  <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                    <% resource.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>

        <!-- Sign Up Link -->
        <div class="mt-8 text-center">
          <p class="text-sm text-gray-600">
            Don't have an account?
            <%= link_to "Create one here", new_registration_path(resource_name), 
                class: "font-medium text-blue-600 hover:text-blue-500 transition-colors" %>
          </p>
        </div>

        <!-- Divider -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Secure Authentication</span>
            </div>
          </div>
        </div>

        <!-- Security Badge -->
        <div class="mt-4 flex justify-center">
          <div class="flex items-center text-xs text-gray-500">
            <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            256-bit SSL encryption
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
