<% content_for :page_header do %>
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-gray-900">Account Settings</h1>
      <nav class="mt-1 flex text-sm text-gray-600">
        <span>QuickFixPro</span>
        <span class="mx-2">/</span>
        <span>Settings</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">Account</span>
      </nav>
    </div>
    <div>
      <%= link_to edit_account_path, class: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
        </svg>
        Edit Settings
      <% end %>
    </div>
  </div>
<% end %>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Left Sidebar -->
  <div class="lg:col-span-1">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <!-- Account Avatar -->
      <div class="flex flex-col items-center">
        <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-3xl font-bold mb-4">
          <%= @account.name[0..1].upcase %>
        </div>
        <h2 class="text-xl font-semibold text-gray-900"><%= @account.name %></h2>
        <p class="text-sm text-gray-500 mt-1">Account ID: <%= @account.id %></p>
      </div>
      
      <!-- Quick Stats -->
      <div class="mt-6 pt-6 border-t border-gray-200 space-y-4">
        <div class="flex justify-between">
          <span class="text-sm text-gray-600">Status</span>
          <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
            <%= @account.status.humanize %>
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-600">Created</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @account.created_at.strftime("%b %d, %Y") %>
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-600">Subdomain</span>
          <span class="text-sm font-medium text-gray-900">
            <%= @account.subdomain %>
          </span>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="mt-6 pt-6 border-t border-gray-200 space-y-2">
        <a href="#" class="block w-full px-4 py-2 text-sm text-center text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
          Download Invoice
        </a>
        <a href="#" class="block w-full px-4 py-2 text-sm text-center text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
          Export Data
        </a>
      </div>
    </div>
  </div>
  
  <!-- Main Content -->
  <div class="lg:col-span-2 space-y-6">
    <!-- Subscription Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Details</h3>
      
      <% if @subscription %>
        <div class="space-y-4">
          <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <div>
              <p class="text-sm text-gray-600">Current Plan</p>
              <p class="text-xl font-semibold text-gray-900 capitalize"><%= @subscription.plan_name %></p>
            </div>
            <div class="text-right">
              <p class="text-sm text-gray-600">Monthly Cost</p>
              <p class="text-xl font-semibold text-gray-900">$<%= @subscription.monthly_price %></p>
            </div>
          </div>
          
          <!-- Usage Limits -->
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-900">Usage & Limits</h4>
            
            <div class="space-y-2">
              <div>
                <div class="flex justify-between text-sm mb-1">
                  <span class="text-gray-600">Websites</span>
                  <span class="text-gray-900 font-medium">
                    <%= @account.websites.count %> / <%= @subscription.usage_limit_for('websites') == Float::INFINITY ? 'Unlimited' : @subscription.usage_limit_for('websites') %>
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full" style="width: <%= [@subscription.usage_percentage_for('websites'), 100].min %>%"></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm mb-1">
                  <span class="text-gray-600">Monthly Audits</span>
                  <span class="text-gray-900 font-medium">
                    <%= @subscription.current_usage_for('monthly_audits') %> / <%= @subscription.usage_limit_for('monthly_audits') == Float::INFINITY ? 'Unlimited' : @subscription.usage_limit_for('monthly_audits') %>
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full" style="width: <%= [@subscription.usage_percentage_for('monthly_audits'), 100].min %>%"></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm mb-1">
                  <span class="text-gray-600">Team Members</span>
                  <span class="text-gray-900 font-medium">
                    <%= @users.count %> / <%= @subscription.usage_limit_for('users') == Float::INFINITY ? 'Unlimited' : @subscription.usage_limit_for('users') %>
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full" style="width: <%= [@subscription.usage_percentage_for('users'), 100].min %>%"></div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="pt-4 border-t border-gray-200">
            <% if @subscription.trial_active? %>
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
                <p class="text-sm text-yellow-800">
                  <strong>Trial Period:</strong> <%= @subscription.days_until_trial_expires %> days remaining
                </p>
              </div>
            <% end %>
            
            <div class="flex space-x-3">
              <a href="#" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                Upgrade Plan
              </a>
              <a href="#" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                View Billing History
              </a>
            </div>
          </div>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <p class="text-gray-600">No active subscription</p>
          <a href="#" class="mt-3 inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
            Start Free Trial
          </a>
        </div>
      <% end %>
    </div>
    
    <!-- Team Members -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Team Members</h3>
        <a href="#" class="text-sm text-blue-600 hover:text-blue-700 font-medium">Invite Member</a>
      </div>
      
      <div class="space-y-3">
        <% @users.each do |user| %>
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                <%= user.first_name[0].upcase %><%= user.last_name[0].upcase %>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900"><%= user.full_name %></p>
                <p class="text-xs text-gray-500"><%= user.email %></p>
              </div>
            </div>
            <div class="text-right">
              <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                <%= user == current_user ? 'You' : 'Member' %>
              </span>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    
    <!-- Security Settings -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Security Settings</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-900">Two-Factor Authentication</p>
            <p class="text-xs text-gray-500">Add an extra layer of security to your account</p>
          </div>
          <button class="px-3 py-1 text-sm text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
            Enable
          </button>
        </div>
        
        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
          <div>
            <p class="text-sm font-medium text-gray-900">API Keys</p>
            <p class="text-xs text-gray-500">Manage your API keys for integrations</p>
          </div>
          <a href="#" class="px-3 py-1 text-sm text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            Manage
          </a>
        </div>
        
        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
          <div>
            <p class="text-sm font-medium text-gray-900">Login Sessions</p>
            <p class="text-xs text-gray-500">View and manage active sessions</p>
          </div>
          <a href="#" class="px-3 py-1 text-sm text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            View Sessions
          </a>
        </div>
      </div>
    </div>
  </div>
</div>