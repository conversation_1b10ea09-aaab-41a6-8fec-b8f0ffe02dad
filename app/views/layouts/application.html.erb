<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Core Meta Tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- Security -->
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <!-- SEO Meta Tags -->
    <%= seo_meta_tags(
      title: @seo&.dig(:title),
      description: @seo&.dig(:description), 
      keywords: @seo&.dig(:keywords),
      canonical_url: @canonical_url,
      noindex: @seo&.dig(:noindex),
      nofollow: @seo&.dig(:nofollow),
      image: @seo&.dig(:image)
    ) %>
    
    <!-- Preload Critical Resources -->
    <%= yield :preload_tags %>
    <%= performance_preload_tags %>
    
    <!-- DNS Prefetch for External Domains -->
    <% if Rails.env.production? %>
      <link rel="dns-prefetch" href="//fonts.googleapis.com">
      <link rel="dns-prefetch" href="//www.googletagmanager.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <% end %>
    
    <!-- Favicons and App Icons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/site.webmanifest">
    <meta name="theme-color" content="#2563eb">
    
    <!-- iOS Safari -->
    <meta name="apple-mobile-web-app-title" content="SpeedBoost">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Microsoft -->
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">
    
    <!-- Stylesheets -->
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    
    <!-- JavaScript -->
    <%= javascript_importmap_tags %>
    
    <!-- Structured Data -->
    <%= yield :structured_data %>
    
    <!-- Additional Head Content -->
    <%= yield :head %>
    
    <% if Rails.env.production? %>
      <!-- Google Analytics 4 (replace with your GA4 measurement ID) -->
      <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID', {
          page_title: '<%= @seo&.dig(:title) || "SpeedBoost" %>',
          custom_map: {
            'dimension1': 'user_type',
            'dimension2': 'account_plan'
          }
        });
        <% if user_signed_in? %>
          gtag('config', 'GA_MEASUREMENT_ID', {
            user_id: '<%= current_user.id %>',
            custom_map: {
              'dimension1': 'authenticated',
              'dimension2': '<%= current_account&.subscription&.plan_name %>'
            }
          });
        <% end %>
      </script>
    <% end %>
  </head>

  <body class="min-h-screen bg-gray-50">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded">
      Skip to main content
    </a>
    
    <!-- Header Navigation -->
    <% unless content_for?(:no_header) %>
      <header class="bg-white shadow-sm border-b" role="banner">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" role="navigation" aria-label="Main navigation">
          <div class="flex justify-between h-16">
            <div class="flex">
              <div class="flex-shrink-0 flex items-center">
                <%= link_to (user_signed_in? ? authenticated_root_path : unauthenticated_root_path), class: "text-2xl font-bold text-blue-600", 'aria-label': 'SpeedBoost Home' do %>
                  SpeedBoost
                <% end %>
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <% if user_signed_in? %>
                <%= link_to "Dashboard", dashboard_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "Websites", websites_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "Account", account_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "Sign out", destroy_user_session_path, method: :delete, class: "bg-red-600 text-white hover:bg-red-700 px-4 py-2 rounded-md text-sm font-medium" %>
              <% else %>
                <%= link_to "Features", features_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "Pricing", pricing_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "About", about_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "Sign in", new_user_session_path, class: "text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium" %>
                <%= link_to "Start Free Trial", new_user_registration_path, class: "bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium" %>
              <% end %>
            </div>
          </div>
        </nav>
      </header>
    <% end %>
    
    <!-- Flash Messages -->
    <% if notice || alert %>
      <div id="flash-messages" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        <% if notice %>
          <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded mb-4" role="alert">
            <%= notice %>
          </div>
        <% end %>
        <% if alert %>
          <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded mb-4" role="alert">
            <%= alert %>
          </div>
        <% end %>
      </div>
    <% end %>
    
    <!-- Main Content -->
    <main id="main-content" class="<%= content_for?(:main_classes) ? yield(:main_classes) : 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8' %>" role="main">
      <%= yield %>
    </main>
    
    <!-- Footer -->
    <% unless content_for?(:no_footer) %>
      <footer class="bg-gray-900 text-white mt-16" role="contentinfo">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="col-span-1 md:col-span-2">
              <h3 class="text-2xl font-bold mb-4">SpeedBoost</h3>
              <p class="text-gray-400 mb-4 max-w-md">
                Professional website performance optimization and Core Web Vitals monitoring. 
                Improve your site speed, search rankings, and user experience.
              </p>
              <div class="flex space-x-4">
                <a href="<%= Rails.application.credentials.dig(:social, :twitter) %>" class="text-gray-400 hover:text-white" aria-label="Follow us on Twitter">
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                  </svg>
                </a>
                <a href="<%= Rails.application.credentials.dig(:social, :linkedin) %>" class="text-gray-400 hover:text-white" aria-label="Follow us on LinkedIn">
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fill-rule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clip-rule="evenodd"/>
                  </svg>
                </a>
              </div>
            </div>
            
            <div>
              <h4 class="text-lg font-semibold mb-4">Product</h4>
              <ul class="space-y-2">
                <li><%= link_to "Features", features_path, class: "text-gray-400 hover:text-white" %></li>
                <li><%= link_to "Pricing", pricing_path, class: "text-gray-400 hover:text-white" %></li>
                <li><%= link_to "Security", security_path, class: "text-gray-400 hover:text-white" %></li>
                <li><a href="/api/docs" class="text-gray-400 hover:text-white">API Documentation</a></li>
              </ul>
            </div>
            
            <div>
              <h4 class="text-lg font-semibold mb-4">Company</h4>
              <ul class="space-y-2">
                <li><%= link_to "About", about_path, class: "text-gray-400 hover:text-white" %></li>
                <li><%= link_to "Contact", contact_path, class: "text-gray-400 hover:text-white" %></li>
                <li><%= link_to "Privacy", privacy_path, class: "text-gray-400 hover:text-white" %></li>
                <li><%= link_to "Terms", terms_path, class: "text-gray-400 hover:text-white" %></li>
              </ul>
            </div>
          </div>
          
          <div class="border-t border-gray-800 pt-8 mt-8">
            <p class="text-gray-400 text-sm text-center">
              © <%= Date.current.year %> SpeedBoost. All rights reserved. 
              Built with ❤️ for faster web experiences.
            </p>
          </div>
        </div>
      </footer>
    <% end %>
    
    <!-- Additional Body Content -->
    <%= yield :body_end %>
  </body>
</html>
