<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Core Meta Tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no">
    
    <!-- Security -->
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <title><%= content_for?(:title) ? yield(:title) : "Dashboard" %> - QuickFixPro</title>
    
    <!-- Favicons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <meta name="theme-color" content="#556ee6">
    
    <!-- Stylesheets -->
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    
    <!-- JavaScript -->
    <%= javascript_importmap_tags %>
    
    <style>
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      
      /* Dropdown animation */
      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .dropdown-enter {
        animation: slideDown 0.2s ease-out;
      }
    </style>
  </head>

  <body class="bg-gray-50" data-turbo="false">
    <!-- Horizontal Header -->
    <header class="bg-white border-b border-gray-200 fixed w-full top-0 z-50">
      <!-- Top Bar -->
      <div class="h-16 px-4 flex items-center justify-between">
        <!-- Left Side -->
        <div class="flex items-center space-x-4">
          <!-- Logo -->
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              QF
            </div>
            <span class="ml-2 text-xl font-semibold text-gray-800">QuickFixPro</span>
          </div>
          
          <!-- Menu Toggle for Mobile -->
          <button class="lg:hidden p-2 rounded-lg hover:bg-gray-100" onclick="toggleMobileMenu()">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
        
        <!-- Right Side -->
        <div class="flex items-center space-x-3">
          <!-- Search -->
          <button class="p-2 rounded-lg hover:bg-gray-100 relative">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </button>
          
          <!-- Notifications -->
          <div class="relative">
            <button class="p-2 rounded-lg hover:bg-gray-100 relative" onclick="toggleDropdown('notifications')">
              <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
              </svg>
              <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-white text-xs flex items-center justify-center">3</span>
            </button>
            
            <!-- Notifications Dropdown -->
            <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 dropdown-enter">
              <div class="p-4 border-b border-gray-100">
                <h3 class="font-semibold text-gray-800">Notifications</h3>
              </div>
              <div class="max-h-96 overflow-y-auto">
                <a href="#" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                      </svg>
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm text-gray-800">New audit completed for example.com</p>
                      <p class="text-xs text-gray-500 mt-1">5 minutes ago</p>
                    </div>
                  </div>
                </a>
                <a href="#" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm text-gray-800">Performance improved by 15%</p>
                      <p class="text-xs text-gray-500 mt-1">1 hour ago</p>
                    </div>
                  </div>
                </a>
                <a href="#" class="block px-4 py-3 hover:bg-gray-50">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                      </svg>
                    </div>
                    <div class="ml-3 flex-1">
                      <p class="text-sm text-gray-800">3 critical issues found</p>
                      <p class="text-xs text-gray-500 mt-1">2 hours ago</p>
                    </div>
                  </div>
                </a>
              </div>
              <div class="p-3 border-t border-gray-100">
                <a href="#" class="text-sm text-blue-600 hover:text-blue-700 font-medium">View all notifications</a>
              </div>
            </div>
          </div>
          
          <!-- Apps Grid -->
          <button class="p-2 rounded-lg hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM13 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z"/>
            </svg>
          </button>
          
          <!-- Settings -->
          <button class="p-2 rounded-lg hover:bg-gray-100">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </button>
          
          <!-- Profile Dropdown -->
          <div class="relative">
            <button class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100" onclick="toggleDropdown('profile')">
              <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                <%= current_user.first_name[0].upcase %><%= current_user.last_name[0].upcase %>
              </div>
              <div class="hidden sm:block text-left">
                <p class="text-sm font-medium text-gray-800"><%= current_user.full_name %></p>
                <p class="text-xs text-gray-500"><%= current_account&.name %></p>
              </div>
              <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
            
            <!-- Profile Dropdown Menu -->
            <div id="profile-dropdown" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 dropdown-enter">
              <div class="p-4 border-b border-gray-100">
                <p class="text-sm font-medium text-gray-800"><%= current_user.full_name %></p>
                <p class="text-xs text-gray-500 mt-1"><%= current_user.email %></p>
              </div>
              <div class="py-1">
                <%= link_to account_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center" do %>
                  <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                  My Profile
                <% end %>
                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center" do %>
                  <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  Settings
                <% end %>
                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center" do %>
                  <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                  Billing
                <% end %>
              </div>
              <div class="py-1 border-t border-gray-100">
                <%= link_to destroy_user_session_path, method: :delete, class: "block px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                  </svg>
                  Sign Out
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Horizontal Navigation Menu -->
      <nav class="bg-gray-50 border-t border-gray-200 hidden lg:block">
        <div class="px-4">
          <ul class="flex space-x-8 h-12 items-center">
            <li>
              <%= link_to dashboard_path, class: "text-sm font-medium #{current_page?(dashboard_path) ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'} transition-colors" do %>
                Dashboard
              <% end %>
            </li>
            
            <!-- Websites Dropdown -->
            <li class="relative group">
              <button class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors flex items-center">
                Websites
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <%= link_to websites_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg" do %>
                  All Websites
                <% end %>
                <%= link_to new_website_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                  Add New Website
                <% end %>
                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg" do %>
                  Performance Reports
                <% end %>
              </div>
            </li>
            
            <!-- Audits Dropdown -->
            <li class="relative group">
              <button class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors flex items-center">
                Audits
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg" do %>
                  All Audits
                <% end %>
                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                  Schedule Audit
                <% end %>
                <%= link_to "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg" do %>
                  Audit History
                <% end %>
              </div>
            </li>
            
            <!-- Optimizations -->
            <li>
              <%= link_to "#", class: "text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors" do %>
                Optimizations
              <% end %>
            </li>
            
            <!-- Analytics -->
            <li>
              <%= link_to "#", class: "text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors" do %>
                Analytics
              <% end %>
            </li>
            
            <!-- Reports -->
            <li>
              <%= link_to "#", class: "text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors" do %>
                Reports
              <% end %>
            </li>
            
            <!-- Settings Mega Menu -->
            <li class="relative group">
              <button class="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors flex items-center">
                Settings
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              <div class="absolute top-full left-0 mt-2 w-[600px] bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 p-6">
                <div class="grid grid-cols-3 gap-6">
                  <div>
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Account</h3>
                    <ul class="space-y-2">
                      <li><%= link_to "Profile Settings", account_path, class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Team Members", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Billing & Plans", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "API Keys", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                    </ul>
                  </div>
                  <div>
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Preferences</h3>
                    <ul class="space-y-2">
                      <li><%= link_to "Notifications", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Email Preferences", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Integrations", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Security", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                    </ul>
                  </div>
                  <div>
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Resources</h3>
                    <ul class="space-y-2">
                      <li><%= link_to "Documentation", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "API Reference", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Support Center", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                      <li><%= link_to "Contact Us", "#", class: "text-sm text-gray-600 hover:text-blue-600" %></li>
                    </ul>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </nav>
    </header>
    
    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="hidden fixed inset-0 z-40 lg:hidden">
      <div class="fixed inset-0 bg-black opacity-50" onclick="toggleMobileMenu()"></div>
      <nav class="fixed top-0 left-0 bottom-0 w-64 bg-white overflow-y-auto">
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <span class="text-lg font-semibold text-gray-800">Menu</span>
            <button onclick="toggleMobileMenu()" class="p-2 rounded-lg hover:bg-gray-100">
              <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="p-4">
          <ul class="space-y-2">
            <li><%= link_to "Dashboard", dashboard_path, class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
            <li><%= link_to "Websites", websites_path, class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
            <li><%= link_to "Audits", "#", class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
            <li><%= link_to "Optimizations", "#", class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
            <li><%= link_to "Analytics", "#", class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
            <li><%= link_to "Reports", "#", class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
            <li><%= link_to "Settings", account_path, class: "block px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100" %></li>
          </ul>
        </div>
      </nav>
    </div>
    
    <!-- Main Content Area -->
    <main class="pt-28 lg:pt-28 min-h-screen">
      <!-- Page Header -->
      <% if content_for?(:page_header) %>
        <div class="bg-white border-b border-gray-200 px-4 py-4">
          <div class="max-w-7xl mx-auto">
            <%= yield(:page_header) %>
          </div>
        </div>
      <% end %>
      
      <!-- Flash Messages -->
      <% if notice || alert %>
        <div class="max-w-7xl mx-auto px-4 pt-4">
          <% if notice %>
            <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-4 flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <%= notice %>
              </div>
              <button onclick="this.parentElement.remove()" class="text-green-600 hover:text-green-800">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          <% end %>
          <% if alert %>
            <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-4 flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
                <%= alert %>
              </div>
              <button onclick="this.parentElement.remove()" class="text-red-600 hover:text-red-800">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          <% end %>
        </div>
      <% end %>
      
      <!-- Page Content -->
      <div class="p-4">
        <div class="max-w-7xl mx-auto">
          <%= yield %>
        </div>
      </div>
    </main>
    
    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-auto">
      <div class="max-w-7xl mx-auto px-4 py-4">
        <div class="flex flex-col sm:flex-row justify-between items-center">
          <p class="text-sm text-gray-600">
            © <%= Date.current.year %> QuickFixPro. All rights reserved.
          </p>
          <div class="flex space-x-6 mt-2 sm:mt-0">
            <a href="#" class="text-sm text-gray-600 hover:text-blue-600">Privacy Policy</a>
            <a href="#" class="text-sm text-gray-600 hover:text-blue-600">Terms of Service</a>
            <a href="#" class="text-sm text-gray-600 hover:text-blue-600">Support</a>
          </div>
        </div>
      </div>
    </footer>
    
    <script>
      function toggleDropdown(id) {
        const dropdown = document.getElementById(id + '-dropdown');
        const allDropdowns = document.querySelectorAll('[id$="-dropdown"]');
        
        allDropdowns.forEach(d => {
          if (d !== dropdown) {
            d.classList.add('hidden');
          }
        });
        
        dropdown.classList.toggle('hidden');
      }
      
      function toggleMobileMenu() {
        const menu = document.getElementById('mobile-menu');
        menu.classList.toggle('hidden');
      }
      
      // Close dropdowns when clicking outside
      document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick*="toggleDropdown"]')) {
          document.querySelectorAll('[id$="-dropdown"]').forEach(d => {
            d.classList.add('hidden');
          });
        }
      });
    </script>
  </body>
</html>