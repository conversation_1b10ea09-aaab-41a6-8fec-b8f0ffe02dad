# Production credentials for SpeedBoost
# Copy this file and customize for your environment
# Run: EDITOR=vim bin/rails credentials:edit --environment=production

app:
  name: "SpeedBoost"
  canonical_host: "speedboost.com"
  domain: "speedboost.com"

seo:
  ready_for_indexing: true
  phone: "******-123-SPEED"

social:
  twitter: "https://twitter.com/speedboost"
  twitter_handle: "@speedboost"
  linkedin: "https://linkedin.com/company/speedboost"
  github: "https://github.com/speedboost"

fonts:
  preload:
    - "/assets/fonts/inter-var.woff2"
    - "/assets/fonts/inter-bold.woff2"

# Database
database:
  host: "localhost"
  username: "speedboost"
  password: "your-secure-password"

# External Services
chrome:
  path: "/usr/bin/google-chrome"
  args: "--headless --disable-gpu --no-sandbox"

smtp:
  host: "smtp.mailgun.org"
  username: "<EMAIL>"
  password: "your-mailgun-api-key"
  port: 587

# Monitoring & Analytics
monitoring:
  sentry_dsn: "https://<EMAIL>/project"
  honeybadger_api_key: "your-honeybadger-key"

# External APIs
apis:
  webpagetest_key: "your-webpagetest-api-key"
  lighthouse_ci_token: "your-lighthouse-ci-token"

# Storage (if using cloud storage for assets)
storage:
  aws_access_key_id: "your-aws-key"
  aws_secret_access_key: "your-aws-secret"
  aws_region: "us-east-1"
  aws_bucket: "speedboost-assets"